#!/bin/bash

echo "Testing Jackson version compatibility..."

# Get the classpath from Gradle
echo "Getting classpath..."
CLASSPATH=$(./gradlew printClasspath --quiet 2>/dev/null)

if [ $? -ne 0 ]; then
    echo "Failed to get classpath from Gradle, trying alternative approach..."
    # Alternative: build a basic classpath
    CLASSPATH="build/classes/kotlin/main"
    for jar in $(find ~/.gradle/caches -name "*.jar" | grep -E "(jackson|spring)" | head -20); do
        CLASSPATH="$CLASSPATH:$jar"
    done
fi

echo "Running Jackson version checker..."
java -cp "build/classes/kotlin/main:$CLASSPATH" com.prospection.refdata.JacksonVersionChecker

echo "Test completed with exit code: $?"
