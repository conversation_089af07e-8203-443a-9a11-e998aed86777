import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("idea")
    id("kotlin")
    kotlin("jvm")
    alias(libs.plugins.shadow)
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

configurations.all {
    // Exclude conflicting logging implementations to use Spring Boot's Logback
    exclude(group = "org.slf4j", module = "slf4j-log4j12")
    exclude(group = "org.slf4j", module = "slf4j-reload4j")
    // Exclude Log4j SLF4J implementations to avoid conflicts with Spring Boot's Logback
    exclude(group = "org.apache.logging.log4j", module = "log4j-slf4j2-impl")
    exclude(group = "org.apache.logging.log4j", module = "log4j-slf4j-impl")
    // Exclude log4j-to-slf4j as it conflicts with log4j-slf4j2-impl
    exclude(group = "org.apache.logging.log4j", module = "log4j-to-slf4j")

    resolutionStrategy {
        force(libs.scala.library)
        force(libs.slf4j.api)
        force(libs.jakatar.servlet.api)
        // Force AWS SDK v2 version
        force("software.amazon.awssdk:bom:${libs.versions.aws.sdk.v2.get()}")

        // Force all AWS SDK v2 components to use the same version
        eachDependency {
            if (requested.group == "software.amazon.awssdk") {
                useVersion(libs.versions.aws.sdk.v2.get())
                because("Force consistent AWS SDK v2 version to avoid NoSuchMethodError")
            }
        }

        // Force all Hadoop components to use the same version
        eachDependency {
            if (requested.group == "org.apache.hadoop") {
                useVersion(libs.versions.hadoop.get())
                because("Force consistent Hadoop version to avoid NoSuchMethodError")
            }
        }
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":kotlin-spark"))
    implementation(project(":scala-spark"))

    implementation(libs.slf4j.api)

    implementation(libs.aws.lambda.core)
    implementation(libs.aws.sdk.lambda)

    implementation(libs.bundles.spark.core)

    implementation(platform(libs.kotlin.bom))
    implementation(libs.bundles.kotlin.core)

    implementation(libs.scala.library)
    implementation(libs.jackson.module.scala)
    implementation(libs.jackson.datatype.jsr310)

    implementation(libs.bundles.aws.sdk)

    implementation(libs.bundles.serialization)

    // Explicitly define JUnit dependencies instead of using the bundle
    testImplementation(libs.junit.platform.launcher)
    testImplementation(libs.junit.jupiter.api)
    testImplementation(libs.junit.jupiter.engine)
    testImplementation(libs.mockito.core)
    testImplementation(libs.mockito.inline)
    testImplementation(libs.mockito.kotlin)
    testImplementation(libs.assertj.core)
}

tasks.shadowJar {
    isZip64 = true

    configurations = listOf(project.configurations.runtimeClasspath.get())

    exclude(
        "assets/**",
        "webapps/**",
        "org/apache/spark/ui/static/**",
        "win*/**",
        "darwin/**",
    )

    exclude(Spec {
        // kotlin-spark module have spring-context dependencies which we just need the annotation at run time
        // hence this is to remove unwanted classes from spring
        it.path.startsWith("org/springframework/")
                && !it.path.startsWith("org/springframework/stereotype")
                && it.path != "org/springframework/beans/factory/annotation/Value.class"
    })
}

tasks.withType<Test> {
    useJUnitPlatform()

    // Add JVM arguments for Spark 4.0.0 compatibility with JDK 17
    jvmArgs(
        "--add-opens=java.base/java.lang=ALL-UNNAMED",
        "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",
        "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
        "--add-opens=java.base/java.io=ALL-UNNAMED",
        "--add-opens=java.base/java.net=ALL-UNNAMED",
        "--add-opens=java.base/java.nio=ALL-UNNAMED",
        "--add-opens=java.base/java.util=ALL-UNNAMED",
        "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED",
        "--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED",
        "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
        "--add-opens=java.base/sun.nio.cs=ALL-UNNAMED",
        "--add-opens=java.base/sun.security.action=ALL-UNNAMED",
        "--add-opens=java.base/sun.util.calendar=ALL-UNNAMED",
        "--add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED"
    )
}

tasks.getByName<Jar>("jar") {
    enabled = false
}

tasks.withType<KotlinCompile> {
    compilerOptions {
        freeCompilerArgs.set(listOf("-Xjsr305=strict"))
        jvmTarget.set(JvmTarget.JVM_17)
    }
}
