plugins {
    id("java-library")
    id("scala")
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

dependencies {
    implementation(libs.spark.sql)
    implementation(libs.scala.logging)

    testImplementation(libs.bundles.scala.test)
}

test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }

    // Configure for ScalaTest
    systemProperty 'java.awt.headless', 'true'
}
