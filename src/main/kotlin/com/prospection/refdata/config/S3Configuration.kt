package com.prospection.refdata.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Lazy
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.presigner.S3Presigner

@Configuration
@Profile("!test")
class S3Configuration {

    @Primary // primary so that the default bean configuration (given by Spring Cloud AWS library) can be overridden
    @Bean
    @Lazy
    @Profile("local")
    fun getLocalS3Client(applicationProperties: ApplicationProperties): S3Client {
        val credentials = AwsBasicCredentials.create(
            applicationProperties.accessKey,
            applicationProperties.secretKey
        )
        return S3Client.builder()
            .endpointOverride(java.net.URI.create(applicationProperties.s3Endpoint))
            .region(Region.of(applicationProperties.awsRegion))
            .credentialsProvider(StaticCredentialsProvider.create(credentials))
            .forcePathStyle(true)
            .build()
    }

    @Bean
    @Lazy
    @Profile("local")
    fun getLocalS3Presigner(applicationProperties: ApplicationProperties): S3Presigner {
        val credentials = AwsBasicCredentials.create(
            applicationProperties.accessKey,
            applicationProperties.secretKey
        )
        return S3Presigner.builder()
            .region(Region.of(applicationProperties.awsRegion))
            .credentialsProvider(StaticCredentialsProvider.create(credentials))
            .endpointOverride(java.net.URI.create(applicationProperties.s3Endpoint))
            .build()
    }

    @Primary // primary so that the default bean configuration (given by Spring Cloud AWS library) can be overridden
    @Bean
    @Lazy
    @Profile("!local")
    fun getS3Client(applicationProperties: ApplicationProperties): S3Client {
        return S3Client.builder()
            .region(Region.of(applicationProperties.awsRegion))
            .credentialsProvider(DefaultCredentialsProvider.create())
            .build()
    }

    @Bean
    @Lazy
    @Profile("!local")
    fun getS3Presigner(applicationProperties: ApplicationProperties): S3Presigner {
        return S3Presigner.builder()
            .region(Region.of(applicationProperties.awsRegion))
            .credentialsProvider(DefaultCredentialsProvider.create())
            .build()
    }
}