package com.prospection.refdata.common.integration

import com.google.common.collect.Sets
import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.items.ScalaSparkItemsFunctions
import com.prospection.refdata.items.domain.ChangeSummary
import com.prospection.refdata.items.domain.ChangeSummaryMetaData
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions
import org.jetbrains.kotlinx.spark.api.lit

object ChangeSummaryHelper {

    private val logger by lazyLogger()

    /**
     * Symmetric difference between two dataframes.
     * If columns in datasets does not match, prune non matching columns and then take the difference
     *
     * @see <a href="https://github.com/G-Research/spark-extension/blob/master/src/main/scala/uk/co/gresearch/spark/diff/Diff.scala">Diff.scala</a>
     * @see <a href="https://github.com/FINRAOS/MegaSparkDiff/blob/master/src/main/scala/org/finra/msd/sparkcompare/SparkCompare.scala">SparkCompare.scala</a>
     * @see <a href="https://bzhangusc.wordpress.com/2016/03/23/create-unique-record-key-for-table-linking/">create-unique-record-key-for-table-linking</a>
     */
    fun compare(
        draftItemsDs: Dataset<Row>,
        publishedItemDs: Dataset<Row>,
    ): ChangeSummary {
        val draftCols = draftItemsDs.columns().toSet() as Set<String>
        val publishedCols = publishedItemDs.columns().toSet() as Set<String>

        val draftExtraCols = Sets.difference(draftCols, publishedCols)
        val publishExtraCols = Sets.difference(publishedCols, draftCols)

        if (!draftExtraCols.isEmpty() || !publishExtraCols.isEmpty()) {
            logger.warn(
                "Columns in draft and published  are different, " +
                    "removing {} from draft, removing {} from published", draftExtraCols, publishExtraCols
            )
        }

        val intersectionCols = Sets.intersection(draftCols, publishedCols).map { functions.col(it) }.toTypedArray()
        // Both draft and published datasets must select the same column to check for differences
        val normalizedDraftDs = ScalaSparkItemsFunctions.addHashUid(draftItemsDs.select(*intersectionCols))
        val normalizedPublishedDs = ScalaSparkItemsFunctions.addHashUid(publishedItemDs.select(*intersectionCols))

        //repartition based on uid to remove partition skew
        normalizedDraftDs.repartition(functions.col("uid"))
        normalizedPublishedDs.repartition(functions.col("uid"))

        val addedRows = normalizedDraftDs.except(normalizedPublishedDs).withColumn("diff", lit("added"))
        val deletedRows = normalizedPublishedDs.except(normalizedDraftDs).withColumn("diff", lit("deleted"))

        return ChangeSummary(
            ChangeSummaryMetaData(draftExtraCols, publishExtraCols, addedRows.count(), deletedRows.count()),
            diffData = addedRows.unionAll(deletedRows).drop("uid")
        )
    }


}