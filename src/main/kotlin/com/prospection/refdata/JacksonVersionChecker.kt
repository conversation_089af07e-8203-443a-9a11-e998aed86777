package com.prospection.refdata

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper

/**
 * Simple utility to verify Jackson version compatibility.
 * This will fail with NoSuchMethodError if Jackson versions are incompatible.
 */
object JacksonVersionChecker {
    
    data class TestClass(
        @JsonProperty(required = true)
        val requiredField: String,
        
        @JsonProperty(required = false)
        val optionalField: String?
    )

    @JvmStatic
    fun main(args: Array<String>) {
        try {
            println("Testing Jackson version compatibility...")
            
            val objectMapper = ObjectMapper()
            
            // Test serialization
            val testObject = TestClass("required", "optional")
            val json = objectMapper.writeValueAsString(testObject)
            println("Serialization successful: $json")
            
            // Test deserialization
            val validJson = """{"requiredField":"test","optionalField":"optional"}"""
            val deserializedObject = objectMapper.readValue(validJson, TestClass::class.java)
            println("Deserialization successful: $deserializedObject")
            
            // Test that JsonProperty annotation can access isRequired() method
            val field = TestClass::class.java.getDeclaredField("requiredField")
            val annotation = field.getAnnotation(JsonProperty::class.java)
            
            // This line would throw NoSuchMethodError if Jackson versions are incompatible
            val isRequired = annotation.required
            println("JsonProperty.isRequired() method is available: $isRequired")
            
            println("✅ Jackson version test PASSED - All Jackson components are compatible!")
            
        } catch (e: NoSuchMethodError) {
            println("❌ Jackson version test FAILED - NoSuchMethodError: ${e.message}")
            println("This indicates Jackson version conflicts.")
            System.exit(1)
        } catch (e: Exception) {
            println("❌ Jackson version test FAILED - Unexpected error: ${e.message}")
            e.printStackTrace()
            System.exit(1)
        }
    }
}
