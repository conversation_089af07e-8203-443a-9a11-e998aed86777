package com.prospection.refdata

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

/**
 * Test to verify Jackson version compatibility and that the isRequired() method is available.
 * This test specifically checks for the NoSuchMethodError that was occurring with Jackson version conflicts.
 */
class JacksonVersionTest {

    data class TestClass(
        @JsonProperty(required = true)
        val requiredField: String,
        
        @JsonProperty(required = false)
        val optionalField: String?
    )

    @Test
    fun `test Jackson isRequired method is available`() {
        // This test will fail with NoSuchMethodError if Jackson versions are incompatible
        val objectMapper = ObjectMapper()
        
        // Test serialization
        val testObject = TestClass("required", "optional")
        val json = objectMapper.writeValueAsString(testObject)
        assertNotNull(json)
        
        // Test deserialization with required field
        val validJson = """{"requiredField":"test","optionalField":"optional"}"""
        val deserializedObject = objectMapper.readValue(validJson, TestClass::class.java)
        assertEquals("test", deserializedObject.requiredField)
        assertEquals("optional", deserializedObject.optionalField)
        
        // Test that JsonProperty annotation can access isRequired() method
        val field = TestClass::class.java.getDeclaredField("requiredField")
        val annotation = field.getAnnotation(JsonProperty::class.java)
        assertNotNull(annotation)
        
        // This line would throw NoSuchMethodError if Jackson versions are incompatible
        val isRequired = annotation.required
        assertTrue(isRequired)
        
        println("Jackson version test passed - isRequired() method is available")
    }
}
