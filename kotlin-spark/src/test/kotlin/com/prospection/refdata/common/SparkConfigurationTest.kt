package com.prospection.refdata.common

import com.prospection.refdata.common.SparkConfig
import org.apache.spark.sql.SparkSession
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.AfterEach

class SparkConfigurationTest {

    private var sparkSession: SparkSession? = null

    @AfterEach
    fun cleanup() {
        sparkSession?.stop()
        sparkSession = null
    }

    @Test
    fun `should create SparkSession with cloud configuration without BindingParquetOutputCommitter error`() {
        // This test verifies that we can create a SparkSession with the cloud configuration
        // that includes the BindingParquetOutputCommitter setting, which was causing the original error
        val sparkConfig = SparkConfig.getCloudSparkConfig()
            .set("spark.ui.enabled", "false") // Disable UI for testing
            .set("spark.driver.bindAddress", "127.0.0.1")

        sparkSession = SparkSession.builder()
            .appName("test-spark-config")
            .config(sparkConfig)
            .orCreate

        assertNotNull(sparkSession, "SparkSession should be created successfully")
        assertNotNull(sparkSession!!.sessionState(), "SessionState should be initialized without errors")
    }

    @Test
    fun `should create SparkSession with default configuration without BindingParquetOutputCommitter error`() {
        // This test verifies that we can create a SparkSession with the default configuration
        // that includes the BindingParquetOutputCommitter setting
        val sparkConfig = SparkConfig.getDefaultSparkConfig()
            .set("spark.ui.enabled", "false") // Disable UI for testing
            .set("spark.driver.bindAddress", "127.0.0.1")

        sparkSession = SparkSession.builder()
            .appName("test-default-spark-config")
            .config(sparkConfig)
            .orCreate

        assertNotNull(sparkSession, "SparkSession should be created successfully")
        assertNotNull(sparkSession!!.sessionState(), "SessionState should be initialized without errors")
    }
}
