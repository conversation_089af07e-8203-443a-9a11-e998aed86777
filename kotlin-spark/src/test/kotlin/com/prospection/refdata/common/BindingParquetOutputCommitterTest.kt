package com.prospection.refdata.common

import org.apache.spark.internal.io.cloud.BindingParquetOutputCommitter
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.assertNotNull

class BindingParquetOutputCommitterTest {

    @Test
    fun `should be able to load BindingParquetOutputCommitter class`() {
        // This test verifies that the BindingParquetOutputCommitter class is available
        // on the classpath, which was the root cause of the original error
        val clazz = BindingParquetOutputCommitter::class.java
        assertNotNull(clazz, "BindingParquetOutputCommitter class should be available")
    }

    @Test
    fun `should be able to instantiate BindingParquetOutputCommitter`() {
        // This test verifies that we can create an instance of the class
        // Note: We're not testing the full functionality here, just that the class can be loaded
        val className = "org.apache.spark.internal.io.cloud.BindingParquetOutputCommitter"
        val clazz = Class.forName(className)
        assertNotNull(clazz, "Should be able to load $className")
    }
}
