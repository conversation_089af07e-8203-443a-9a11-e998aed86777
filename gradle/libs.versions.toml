[versions]
kotlin = "2.1.0"
scala = "2.13.13"
spark = "4.0.0"
spark-excel = "3.5.1_0.20.4"
scala-logging = "3.9.5"
hadoop = "3.4.1"
aws-sdk-v2 = "2.21.46" # should match aws sdk version used in Spring Cloud AWS
junit-jupiter = "5.10.2"
mockito = "5.2.0"
assertj = "3.25.3"
spring-boot = "3.4.2"
spring = "6.4.2"
pd-boot-starter = "2.12.0"
pd-boot-starter-jpa = "2.18.0"
pd-boot-starter-tracing = "3.10.0"
pd-boot-starter-web-security = "4.22.0"
springdoc-openapi-starter-webmvc-ui = "2.5.0"
awspring-cloud-starter-sqs = "3.1.1"
sonar = "6.0.1.5171"
git-properties = "2.4.2"
shadow = "8.3.6"
liquibase-core = "4.27.0"
jakarta-validation-api = "3.0.2"
jakarta-persistence-api = "3.1.0"
kotlin-spark-api = "1.2.4"
unleash = "9.3.2"
jooq = "3.19.7"
hypersistence-utils = "3.9.10"
slf4j = "2.0.13"
mapstruct = "1.5.5.Final"
lombok = "1.18.32"
lombok-mapstruct-binding = "0.2.0"
kotlinx-serialization = "1.6.0"
testcontainers = "1.19.8"
univocity-parsers = "2.9.1"
scalatest = "3.2.18"
flexmark = "0.64.8"
scalatestplus-junit = "3.2.19.1"
scalatestplus-mockito = "3.2.19.0"
apache-poi = "5.4.1"
spring-dependency-management = "1.1.7"
dependency-license-report = "2.9"
jakarta-servlet-api = "5.0.0" # Jakarta Servlet API 5.0.0 for Spark UI compatibility
logstash-logback-encoder = "8.1"
logback = "1.5.18"
jackson = "2.18.2"  # Match Spring Boot 3.4.2 Jackson version
junit-platform-launcher = "1.13.1"

[plugins]
spring-boot = { id = "org.springframework.boot", version.ref = "spring-boot" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
kotlin-plugins-allopen = { id = "org.jetbrains.kotlin.plugin.allopen", version.ref = "kotlin" }
kotlin-plugins-jpa = { id = "org.jetbrains.kotlin.plugin.jpa", version.ref = "kotlin" }
kotlin-plugins-spring = { id = "org.jetbrains.kotlin.plugin.spring", version.ref = "kotlin" }
kotlin-plugins-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
sonar = { id = "org.sonarqube", version.ref = "sonar" }
git-properties = { id = "com.gorylenko.gradle-git-properties", version.ref = "git-properties" }
shadow = { id = "com.gradleup.shadow", version.ref = "shadow" }
spring-dependency-management = { id = "io.spring.dependency-management", version.ref = "spring-dependency-management" }
dependency-license-report = { id = "com.github.jk1.dependency-license-report", version.ref = "dependency-license-report" }

[libraries]
kotlin-bom = { module = "org.jetbrains.kotlin:kotlin-bom", version.ref = "kotlin" }
kotlinStdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib", version.ref = "kotlin" }
kotlinStdlibJdk8 = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8", version.ref = "kotlin" }
kotlinReflect = { module = "org.jetbrains.kotlin:kotlin-reflect", version.ref = "kotlin" }
mapstruct = { module = "org.mapstruct:mapstruct", version.ref = "mapstruct" }
mapstruct-processor = { module = "org.mapstruct:mapstruct-processor", version.ref = "mapstruct" }
mapstruct-kotlin = { module = "org.jetbrains.kotlin:kotlin-annotation-processing-embeddable", version.ref = "kotlin" }
lombok = { module = "org.projectlombok:lombok", version.ref = "lombok" }
lombok-mapstruct-binding = { module = "org.projectlombok:lombok-mapstruct-binding", version.ref = "lombok-mapstruct-binding" }
pd-boot-starter = { module = "com.prospection:pd-boot-starter", version.ref = "pd-boot-starter" }
pd-boot-starter-jpa = { module = "com.prospection:pd-boot-starter-jpa", version.ref = "pd-boot-starter-jpa" }
pd-boot-starter-tracing = { module = "com.prospection:pd-boot-starter-tracing", version.ref = "pd-boot-starter-tracing" }
pd-boot-starter-web-security = { module = "com.prospection:pd-boot-starter-web-security", version.ref = "pd-boot-starter-web-security" }
spring-boot-starter-actuator = { module = "org.springframework.boot:spring-boot-starter-actuator", version.ref = "spring-boot" }
spring-boot-starter-logging = { module = "org.springframework.boot:spring-boot-starter-logging", version.ref = "spring-boot" }
aws-sdk-s3 = { module = "software.amazon.awssdk:s3", version.ref = "aws-sdk-v2" }
aws-sdk-core = { module = "software.amazon.awssdk:core", version.ref = "aws-sdk-v2" }
aws-sdk-sns = { module = "software.amazon.awssdk:sns", version.ref = "aws-sdk-v2" }
aws-sdk-glue = { module = "software.amazon.awssdk:glue", version.ref = "aws-sdk-v2" }
aws-sdk-lambda = { module = "software.amazon.awssdk:lambda", version.ref = "aws-sdk-v2" }
aws-sdk-apache-client = { module = "software.amazon.awssdk:apache-client", version.ref = "aws-sdk-v2" }
aws-sdk-s3-transfer-manager = { module = "software.amazon.awssdk:s3-transfer-manager", version.ref = "aws-sdk-v2" }
aws-lambda-core = { module = "com.amazonaws:aws-lambda-java-core", version = "1.2.3" }
scala-library = { module = "org.scala-lang:scala-library", version.ref = "scala" }
scala-logging = { module = "com.typesafe.scala-logging:scala-logging_2.13", version.ref = "scala-logging" }
spark-sql = { module = "org.apache.spark:spark-sql_2.13", version.ref = "spark" }
spark-hadoop-cloud = { module = "org.apache.spark:spark-hadoop-cloud_2.13", version.ref = "spark" }
spark-excel = { module = "com.crealytics:spark-excel_2.13", version.ref = "spark-excel" }
hadoop-aws = { module = "org.apache.hadoop:hadoop-aws", version.ref = "hadoop" }
kotlin-spark-api = { module = "org.jetbrains.kotlinx.spark:kotlin-spark-api_3.3.2_2.13", version.ref = "kotlin-spark-api" }
junit-jupiter-api = { module = "org.junit.jupiter:junit-jupiter-api", version.ref = "junit-jupiter" }
junit-jupiter-engine = { module = "org.junit.jupiter:junit-jupiter-engine", version.ref = "junit-jupiter" }
junit-platform-launcher = { module = "org.junit.platform:junit-platform-launcher", version.ref = "junit-platform-launcher" }
mockito-core = { module = "org.mockito:mockito-core", version.ref = "mockito" }
mockito-inline = { module = "org.mockito:mockito-inline", version.ref = "mockito" }
mockito-kotlin = { module = "org.mockito.kotlin:mockito-kotlin", version.ref = "mockito" }
assertj-core = { module = "org.assertj:assertj-core", version.ref = "assertj" }
slf4j-api = { module = "org.slf4j:slf4j-api", version.ref = "slf4j" }
liquibase-core = { module = "org.liquibase:liquibase-core", version.ref = "liquibase-core" }
springdoc-openapi-starter-webmvc-ui = { module = "org.springdoc:springdoc-openapi-starter-webmvc-ui", version.ref = "springdoc-openapi-starter-webmvc-ui" }
jakarta-validation-api = { module = "jakarta.validation:jakarta.validation-api", version.ref = "jakarta-validation-api" }
jakarta-persistence-api = { module = "jakarta.persistence:jakarta.persistence-api", version.ref = "jakarta-persistence-api" }
unleash = { module = "io.getunleash:unleash-client-java", version.ref = "unleash" }
awspring-cloud-starter-sqs = { module = "io.awspring.cloud:spring-cloud-aws-starter-sqs", version.ref = "awspring-cloud-starter-sqs" }
jooq = { module = "org.jooq:jooq", version.ref = "jooq" }
hypersistence-utils = { module = "io.hypersistence:hypersistence-utils-hibernate-63", version.ref = "hypersistence-utils" }
spring-security-test = { module = "org.springframework.security:spring-security-test", version.ref = "spring" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinx-serialization" }
kotlinx-serialization-core = { module = "org.jetbrains.kotlinx:kotlinx-serialization-core", version.ref = "kotlinx-serialization" }
univocity-parsers = { module = "com.univocity:univocity-parsers", version.ref = "univocity-parsers" }
scalatest = { module = "org.scalatest:scalatest_2.13", version.ref = "scalatest" }
flexmark-all = { module = "com.vladsch.flexmark:flexmark-all", version.ref = "flexmark" }
scalatestplus-junit = { module = "org.scalatestplus:junit-5-10_2.13", version.ref = "scalatestplus-junit" }
scalatestplus-mockito = { module = "org.scalatestplus:mockito-5-12_3", version.ref = "scalatestplus-mockito" }
poi = { module = "org.apache.poi:poi", version.ref = "apache-poi" }
poi-ooxml = { module = "org.apache.poi:poi-ooxml", version.ref = "apache-poi" }
jakatar-servlet-api = { module = "jakarta.servlet:jakarta.servlet-api", version.ref = "jakarta-servlet-api" }
logstash-logback-encoder = { module = "net.logstash.logback:logstash-logback-encoder", version.ref = "logstash-logback-encoder" }
logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logback" }
logback-core = { module = "ch.qos.logback:logback-core", version.ref = "logback" }
# Use Spring Boot 3.4.2 compatible Jackson version
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "jackson" }
jackson-module-scala = { module = "com.fasterxml.jackson.module:jackson-module-scala_2.13", version.ref = "jackson" }
jackson-datatype-jsr310 = { module = "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", version.ref = "jackson" }
jackson-core = { module = "com.fasterxml.jackson.core:jackson-core", version.ref = "jackson" }
jackson-databind = { module = "com.fasterxml.jackson.core:jackson-databind", version.ref = "jackson" }
jackson-annotations = { module = "com.fasterxml.jackson.core:jackson-annotations", version.ref = "jackson" }

[bundles]
pd-boot-starters = [
    "pd-boot-starter",
    "pd-boot-starter-jpa",
    "pd-boot-starter-tracing",
    "pd-boot-starter-web-security",
    "spring-boot-starter-actuator",
    "spring-boot-starter-logging"
]

scala-test = [
    "scalatest",
    "flexmark-all",
    "scalatestplus-junit",
    "scalatestplus-mockito"
]

# Apache POI for Excel processing
apache-poi = [
    "poi",
    "poi-ooxml"
]

aws-sdk = [
    "aws-sdk-s3",
    "aws-sdk-core",
    "aws-sdk-sns",
    "aws-sdk-glue",
    "aws-sdk-lambda",
    "aws-sdk-apache-client",
    "aws-sdk-s3-transfer-manager"
]

kotlin-core = [
    "kotlinReflect",
    "kotlinStdlib",
    "kotlinStdlibJdk8"
]

spark-core = [
    "spark-sql",
    "spark-hadoop-cloud",
    "hadoop-aws",
    "kotlin-spark-api"
]

serialization = [
    "kotlinx-serialization-json",
    "kotlinx-serialization-core",
    "jackson-module-kotlin"
]
